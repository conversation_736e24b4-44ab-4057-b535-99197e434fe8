<script setup lang="ts">
// 1.1 引入Vditor 构造函数
import Vditor from 'vditor';
// 1.2 引入样式
import 'vditor/dist/index.css';
// 1.3 引入Vue
import { ref, onMounted, defineProps, defineExpose, defineModel, watch, withDefaults } from 'vue';
// 引入HTTP模块
import { http } from '@/apis';
// 引入Element Plus的消息通知组件
import { ElMessage } from 'element-plus';
import { revertMathScriptsToMd,unescapeLatexFormulas} from '@/utils/latexUtils';
interface VeditorProps {
  disabled?: boolean;
  height?: number;
  mode?: 'ir' | 'wysiwyg' | 'sv';
  placeholder?: string;
}
const props = withDefaults(defineProps<VeditorProps>(), {
  disabled: false,
  height: 300,
  mode: 'wysiwyg',
  placeholder: '请输入'
});

// 2. 获取DOM引用
const vditor = ref<Vditor>();
const model = defineModel<string>(); // 指定model的类型为string
const isReady = ref(false);

// 3. 在组件初始化时，就创建Vditor对象，并引用
onMounted(() => {
  vditor.value = new Vditor('vditor', {
    height: props.height,
    width: '100%',
    mode: props.mode,
    cache: {
      enable: false
    },

    minHeight: props.height,
    toolbar: [
      'headings',
      'bold',
      'italic',
      'strike',
      'link',
      '|',
      'list',
      'ordered-list',
      'check',
      'outdent',
      'indent',
      '|',
      'table',
      'quote',
      'line',
      'code',
      'inline-code',
      '|',
      'upload',
      // 'edit-mode',
      'both',
      'emoji',
      '|',
      'undo',
      'redo'
      // "|",
      // "fullscreen",
      // "preview",
      // "outline",
      // "code-theme",
      // "content-theme",
      // "export",
      // "help",
    ],
    hint: {
      extend: []
    },
    // 配置图片上传
    // 配置图片上传
    upload: {
      // 上传图片时的回调函数
      accept: 'image/jpeg,image/png,image/gif,image/jpg,image/bmp', // 图片格式
      max: 1 * 1024 * 1024, // 控制大小 (1MB)
      multiple: true, // 是否允许批量上传
      linkToImgUrl: '/upload/file',
      handler: async (files: File[]) => {
        try {
          // 创建一个数组来存储所有上传成功的图片信息
          const uploadResults = [];

          // 遍历对每个文件单独上传
          for (const file of files) {
            // 在try块外定义安全文件名，确保catch块中可以访问
            let safeFileName = '';
            try {
              // 对文件名进行安全处理
              safeFileName = safeFilename(file.name);

              // 检查文件大小是否超过限制
              if (isFileSizeExceeded(file)) {
                console.error(`文件 ${safeFileName} 大小超过限制:`, file.size);

                // 显示上传失败通知
                ElMessage({
                  message: `图片 ${safeFileName} 上传失败：文件大小超过1MB限制`,
                  type: 'error',
                  duration: 3000
                });

                uploadResults.push({
                  filename: safeFileName,
                  success: false,
                  error: '文件大小超过1MB限制'
                });

                continue; // 跳过此文件的上传
              }

              // 创建一个新的File对象，使用安全处理后的文件名
              const safeFile = new File([file], safeFileName, {
                type: file.type
              });

              const formData = new FormData();
              formData.append('file', safeFile);

              // 上传接口 - 每个文件单独调用，并设置更长的超时时间
              const response = await http<any>({
                url: '/upload/file',
                method: 'post',
                data: formData
              });

              // 处理响应，提取URL
              let imageUrl = '';
              if (response.data) {
                imageUrl = response.data.url;
              }

              if (imageUrl) {
                console.log('插入图片URL:', imageUrl);

                // 使用已经安全处理过的文件名
                const filename = safeFileName;

                // 无论什么模式都使用Markdown格式插入图片
                const insertContent = `![${filename}](${imageUrl})\n`;

                // 将内容插入到编辑器
                vditor.value?.insertValue(insertContent);

                // 显示上传成功通知
                ElMessage({
                  message: `图片 ${filename} 上传成功`,
                  type: 'success',
                  duration: 2000
                });

                // 保存上传结果
                uploadResults.push({
                  filename,
                  url: imageUrl,
                  success: true
                });
              } else {
                console.error('上传成功但未获取到图片URL:', response);

                // 显示上传失败通知
                ElMessage({
                  message: `图片 ${safeFileName} 上传失败：未获取到图片URL`,
                  type: 'error',
                  duration: 3000
                });

                uploadResults.push({
                  filename: safeFileName,
                  success: false,
                  error: '上传成功但未获取到图片URL'
                });
              }
            } catch (error) {
              // 如果safeFileName为空，说明在安全处理文件名时失败，需要重新处理
              if (!safeFileName) {
                safeFileName = safeFilename(file.name);
              }
              console.error(`文件 ${safeFileName} 上传失败:`, error);

              // 显示上传失败通知
              let errorMessage = '上传失败';
              if (error instanceof Error) {
                errorMessage = error.message;
                // 如果是超时错误，提供更有用的提示
                if (errorMessage.includes('timeout')) {
                  errorMessage = `上传超时，请检查网络连接或尝试分批上传`;
                }
              }

              ElMessage({
                message: `图片 ${safeFileName} 上传失败：${errorMessage}`,
                type: 'error',
                duration: 3000
              });

              uploadResults.push({
                filename: safeFileName,
                success: false,
                error: errorMessage
              });
            }
          }

          console.log('所有文件上传结果:', uploadResults);
          // 返回空字符串，表示我们已经手动处理了图片插入，不需要Vditor再次处理
          return '';
        } catch (error) {
          console.error('Vditor图片上传错误:', error);

          // 处理错误并显示通知
          let errorMessage = '上传出错';
          if (error instanceof Error) {
            errorMessage = error.message;
            // 如果是超时错误，提供更有用的提示
            if (errorMessage.includes('timeout')) {
              errorMessage = `上传超时，请检查网络连接或尝试分批上传`;
            }
          }

          ElMessage({
            message: `图片上传失败：${errorMessage}`,
            type: 'error',
            duration: 3000
          });

          return JSON.stringify({
            msg: errorMessage,
            code: 1,
            data: {
              errFiles: files.map((file) => file.name),
              succMap: {}
            }
          });
        }
      }
    },
    after: () => {
      isReady.value = true;
      if (model.value) {
        vditor.value?.setValue(model.value);
      }
    },
    input: (value: string) => {
      model.value = value;
    },
    placeholder: props.placeholder,
    preview: {
      delay: 500,
      mode: 'both',
      actions: []
    }
  });

  // 设置禁用状态
  if (props.disabled) {
    vditor.value.disabled();
  }
});
// 监听model变化
watch(
  () => model.value,
  (newVal) => {
    if (vditor.value && isReady.value && newVal !== vditor.value.getValue()) {
      vditor.value.setValue(newVal || '');
    }
  },
  { deep: true }
);

// 文件名安全处理
// 定义一个文件名安全处理函数,接收一个字符串参数,返回处理后的字符串
const safeFilename = (name: string): string => {
  return (
    name
      // 只保留英文字母、数字、中文和点号
      .replace(/[^(a-zA-Z0-9\u4e00-\u9fa5\.)]/g, '')
      // 移除特殊字符(?/\:|<>*[]()${}@~)
      .replace(/[\?\\/:|<>\*\[\]\(\)\$%\{\}@~]/g, '')
      // 移除空白字符
      .replace('/\\s/g', '')
  );
};

// 检查文件大小是否超过限制
const isFileSizeExceeded = (file: File, maxSize: number = 1 * 1024 * 1024): boolean => {
  const result = file.size > maxSize;
  if (result) {
    console.log(`文件大小检查: ${file.name}, 大小: ${file.size}字节, 超过1MB限制`);
  }
  return result;
};
// 实现与 classicCKEditor 相同的方法
const getData = (): string => {
  if (vditor.value) {
    return vditor.value.getValue();
  }
  return '';
};
const getHtml = (): string => {
  if (vditor.value) {
    return vditor.value.getHTML();
  }
  return '';
};

const html2Md = (html: string): string => {
  if (vditor.value && isReady.value) {
    return revertMathScriptsToMd(vditor.value.html2md(html));
  }
  return '';
};


// 延迟转换HTML为Markdown的方法
const html2MdWhenReady = (html: string): Promise<string> => {
  return new Promise((resolve) => {
    if (vditor.value && isReady.value) {
      // 如果已经就绪，立即转换
      console.log('html', html);
      let result = vditor.value.html2md(revertMathScriptsToMd(html));
      // 处理LaTeX公式转义问题
      result = unescapeLatexFormulas(result);
      resolve(result);
    } else {
      // 如果未就绪，等待就绪后转换
      const checkReady = () => {
        if (vditor.value && isReady.value) {
          console.log('html', html);

          let result = vditor.value.html2md(revertMathScriptsToMd(html));
          // 处理LaTeX公式转义问题
          result = unescapeLatexFormulas(result);
          resolve(result);
        } else {
          setTimeout(checkReady, 100);
        }
      };
      checkReady();
    }
  });
};
const setData = (value: string, dis?: boolean) => {
  if (vditor.value && isReady.value) {
    vditor.value.setValue(value);
    if (dis !== undefined) {
      if (dis) {
        vditor.value.disabled();
      } else {
        vditor.value.enable();
      }
    }
  }
};

// 暴露方法
defineExpose({
  getData,
  getHtml,
  setData,
  html2Md,
  html2MdWhenReady
});
</script>

<template>
  <!-- 指定一个容器 -->
  <div id="vditor" class="vditor-container"></div>
  <!-- 添加一个按钮来获取内容 -->
  <!-- <button @click="getContent">获取内容</button> -->
</template>

<style scoped>
.vditor-container {
  border: 1px solid #ccced1;
  border-radius: 5px;
  box-sizing: border-box;
  height: v-bind('props.height + "px"');
  display: flex; /* 添加flex布局 */
  flex-direction: column; /* 垂直方向排列 */
}

/* 移除固定高度计算 */
:deep(.vditor-content) {
  flex: 1; /* 自动填充剩余空间 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 防止内容溢出 */
}

:deep(.vditor-reset) {
  padding: 0 10px !important;
  flex: 1; /* 自动填充剩余空间 */
  overflow: auto; /* 内容过多时显示滚动条 */
  width: 100% !important;
  /* 修复列表样式 */
  & ul {
    list-style-type: disc !important;
    padding-left: 20px !important;
  }

  & ol {
    list-style-type: decimal !important;
    padding-left: 20px !important;
  }

  /* 修复列表项标记 */
  & li::marker {
    content: initial !important;
    color: initial !important;
  }
}

/* 编辑区域也使用flex布局 */
:deep(.vditor-ir),
:deep(.vditor-wysiwyg) {
  flex: 1; /* 自动填充剩余空间 */
  min-height: 0; /* 防止内容溢出 */
  overflow: hidden; /* 内容过多时显示滚动条 */
}

/* 确保所有子元素都使用border-box盒模型 */
:deep(*) {
  box-sizing: border-box;
}
</style>
